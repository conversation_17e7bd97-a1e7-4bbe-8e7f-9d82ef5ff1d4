import os
import async<PERSON>
from threading import Thread

import uvicorn

from contextlib import asynccontextmanager
from dotenv import load_dotenv
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from qdrant_client import QdrantClient

import logging

load_dotenv()
logging.basicConfig(
    filename='main.log',
    level=logging.INFO
)

def _run_uvicorn() -> None:
    uvicorn.run(
        app,
        host='0.0.0.0',
        port=8000
    )

@asynccontextmanager
async def lifespan(app: FastAPI):
    logging.info("Connecting to Qdrant Cloud...\n")

    try:
        app.state.qdrant_client = QdrantClient(
            url=os.getenv("QDRANT_ENDPOINT"),
            api_key=os.getenv("QDRANT_API"),
        )
    except Exception as error:
        logging.error(f"Error connecting to Qdrant Cloud: {error}")

    try:
        yield
    finally:
        client = getattr(app.state, "qdrant_client", None)
        if client:
            try:
                client.close()
            except Exception as e:
                logging.error(f"Error closing Qdrant client: {e}")

app = FastAPI(lifespan=lifespan)
allowed_origins = os.getenv("ALLOWED_ORIGINS", '').split(",") \
    if os.getenv("APP_ENV") == "production" \
    else os.getenv("ALLOWED_ORIGINS_DEV", '').split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)


if __name__ == '__main__':
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = None

    if loop and loop.is_running():
        # Running under a debugger/interactive environment -> start server in a background thread
        t = Thread(target=_run_uvicorn, daemon=True)
        t.start()
        t.join()
    else:
        # Normal process start -> run normally (uses asyncio.run internally)
        uvicorn.run(app, host='0.0.0.0', port=8000)
